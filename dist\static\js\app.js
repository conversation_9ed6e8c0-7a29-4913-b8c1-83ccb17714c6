webpackJsonp([12],{JXLx:function(e,n){},NHnr:function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var a=t("7+uW"),o={render:function(){var e=this.$createElement,n=this._self._c||e;return n("div",{attrs:{id:"app"}},[n("keep-alive",{attrs:{include:["Aboutme","Message","FriendsLink","Reward"]}},[n("router-view")],1)],1)},staticRenderFns:[]};var i=t("VU/8")({name:"App"},o,!1,function(e){t("JXLx")},null,null).exports,r=t("/ocq");a.default.use(r.a);var u=new r.a({scrollBehavior:function(e,n,t){return t||{x:0,y:window.innerWidth>=700?676:267}},routes:[{path:"/",component:function(e){return Promise.all([t.e(0),t.e(1)]).then(function(){var n=[t("HXef")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"Home"},{path:"/Home",component:function(e){return Promise.all([t.e(0),t.e(1)]).then(function(){var n=[t("HXef")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"Home"},{path:"/Share",component:function(e){return Promise.all([t.e(0),t.e(2)]).then(function(){var n=[t("zJHd")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"Share"},{path:"/DetailShare",component:function(e){return Promise.all([t.e(0),t.e(6)]).then(function(){var n=[t("vo1k")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"DetailShare"},{path:"/Reward",component:function(e){return Promise.all([t.e(0),t.e(4)]).then(function(){var n=[t("gejy")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"Reward"},{path:"/FriendsLink",component:function(e){return Promise.all([t.e(0),t.e(8)]).then(function(){var n=[t("pUly")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"FriendsLink"},{path:"/Message",component:function(e){return Promise.all([t.e(0),t.e(3)]).then(function(){var n=[t("kEhC")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"Message"},{path:"/Aboutme",component:function(e){return Promise.all([t.e(0),t.e(5)]).then(function(){var n=[t("caxs")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"Aboutme"},{path:"/Login",component:function(e){return Promise.all([t.e(0),t.e(9)]).then(function(){var n=[t("P7ry")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!1},name:"Login"},{path:"/UserInfo",component:function(e){return Promise.all([t.e(0),t.e(10)]).then(function(){var n=[t("psK5")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"UserInfo"},{path:"/LikeCollect",component:function(e){return Promise.all([t.e(0),t.e(7)]).then(function(){var n=[t("vjmD")];e.apply(null,n)}.bind(this)).catch(t.oe)},meta:{auth:!0},name:"LikeCollect"}]}),l=t("zL8q"),c=t.n(l),h=(t("q8zI"),t("PijW"),t("NYxO"));a.default.use(h.a);var p={loading:!1,UserList:[111,222,333],themeObj:0,aboutmeObj:"",host:"http://"+window.location.host+"/port/",keywords:"",errorImg:'this.onerror=null;this.src="'+t("jQBE")+'"'},s=new h.a.Store({state:p});a.default.config.productionTip=!1,a.default.use(c.a),new a.default({el:"#app",router:u,components:{App:i},template:"<App/>",store:s})},PijW:function(e,n){},jQBE:function(e,n,t){e.exports=t.p+"static/img/tou.jpg?v=9656b6d"},q8zI:function(e,n){}},["NHnr"]);