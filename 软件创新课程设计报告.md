---
typora-root-url: ./F:\Typora_images
---



<div align = "center"><h1>莆田学院课程考核评分表</h1></div>

<div align = "center"><h1>2024— 2025学年 第 二 学期</h1></div>



### 课程名称：<u>软件设计与开发创新实践</u>           考核环节：<u>期末考核</u></u>  

 

### 小  组：<u>班级：软工224,组长：苗全,组员：陈梓杰,李尚健</u>      

 

### 考核题目：<u>基于SpringBoot的某博客系统的设计与实现</u>  











| 构成     | 内容及评分标准                                               | 成绩 |
| -------- | ------------------------------------------------------------ | ---- |
| 格式     | 段落层次分明，文字大小合适，系统截图清晰（10%）              |      |
| 内容     | 先文字描述，再图片展示，文字精炼，图片准确（10%）            |      |
| 心得体会 | 描述项目开发过程中遇到的问题、得到的收获、不足之处改进方向（10%） |      |
| 系统设计 | 系统组成、角色用例、模块功能、数据库设计等，展示用例图、流程图、ER图等（30%） |      |
| 系统实现 | 展示各模块的实现效果，由文字描述，页面截图，关键代码和接口描述构成（40%） |      |
|          | 合计                                                         |      |

<hr STYLE="page-break-after: always;"></hr>







## 评语:

#### 该小组按时提交了作品与报告，完成了基于SpringBoot的某博客系统的设计与实现。该报告段落层次<font size="6">□</font>分明<font size="6">□</font>不清晰<font size="6">□</font>混乱，报告格式<font size="6">□</font>整齐<font size="6">□</font>一般<font size="6">□</font>混乱，文章内容<font size="6">□</font>详实<font size="6">□</font>一般<font size="6">□</font>简略，系统结构<font size="6">□</font>合理<font size="6">□</font>欠考虑<font size="6">□</font>不合理，系统功能<font size="6">□</font>多<font size="6">□</font>一般<font size="6">□</font>少，逻辑<font size="6">□</font>通顺<font size="6">□</font>一般<font size="6">□</font>不通顺，实现效果<font size="6">□</font>美观<font size="6">□</font>一般<font size="6">□</font>简陋。经综合考量，最终认定该同学成绩如上。

| 教师签名： | <img  height="50px" width="150px" src="data:image/jpeg;base64,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" /> | <img height="50px" width="150px" src="data:image/jpeg;base64,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" /> | <img height="50px" width="150px" src="data:image/png;base64,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" /> |
| ---------- | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |

 																																				2025 年 6月 29日





### 复核人意见：

<div align = "center"><img height="150px" width="250px" src="data:image/png;base64,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" /></div>

| 教师签名： | <img height="50px" width="150px" src="data:image/png;base64,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" /> |
| ---------- | ------------------------------------------------------------ |

​                                                                                                                                                 2025 年 6月 30日



<hr STYLE="page-break-after: always;"></hr>

# 1.心得体会

（1）小组分工 

（2）开发过程中遇到的问题和解决方法 

（3）项目完成的收获 

（4）系统的不足之处 

（5）系统的改进方向 



# 2.用例模型

文字描述：根据对象模型获取到的对象和用例，画图相关的符号要正确，参考软工教材电子版《软件工程导论》P238

<img src="data:image/jpeg;base64,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" />

![](莆田学院课程考核评分表.assets/用例描述.jpg)

# 3.系统设计

## 3.1数据库设计

参考《高校教学管理系统-数据库设计案例参考.pdf》

大概描述下数据库技术选型，数据库设计的一些注意事项，比如安全性、完整性、可靠性等。

### 3.1.1概念结构设计

文字描述部分：有哪些实体，实体的属性

局部E-R图

![](莆田学院课程考核评分表.assets/局部E-R图.png)

全局E-R图（注意：若没保存管理员对实体管理的信息，不要画管理员实体，类似高校教学管理系统中没画教务员实体）

![](莆田学院课程考核评分表.assets/全局E-R图.png)

ER图绘制规则补充：

![](莆田学院课程考核评分表.assets/泛化关系.png)

![](莆田学院课程考核评分表.assets/组合关系.png)

### 3.1.2逻辑结构设计

##### 表1: article (文章表)

此表用于存储博客文章的核心数据，包括文章的标题、正文内容、摘要、所属分类、发布状态以及相关的元数据，如浏览量和评论设置。

| **字段名**  | **数据类型**  | **主键或外键** | **字段值约束**           | **说明**                             |
| ----------- | ------------- | -------------- | ------------------------ | ------------------------------------ |
| id          | bigint        | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT |                                      |
| title       | varchar(256)  |                | NULL DEFAULT NULL        | 标题                                 |
| content     | longtext      |                | NULL                     | 文章内容                             |
| summary     | varchar(1024) |                | NULL DEFAULT NULL        | 文章摘要                             |
| category_id | bigint        |                | NULL DEFAULT NULL        | 所属分类id                           |
| thumbnail   | varchar(256)  |                | NULL DEFAULT NULL        | 缩略图                               |
| is_top      | char(1)       |                | NULL DEFAULT '0'         | 是否置顶（0否，1是）                 |
| status      | char(1)       |                | NULL DEFAULT '1'         | 状态（0已发布，1草稿）               |
| view_count  | bigint        |                | NULL DEFAULT 0           | 访问量                               |
| is_comment  | char(1)       |                | NULL DEFAULT '1'         | 是否允许评论 1是，0否                |
| create_by   | bigint        |                | NULL DEFAULT NULL        |                                      |
| create_time | datetime      |                | NULL DEFAULT NULL        |                                      |
| update_by   | bigint        |                | NULL DEFAULT NULL        |                                      |
| update_time | datetime      |                | NULL DEFAULT NULL        |                                      |
| del_flag    | int           |                | NULL DEFAULT 0           | 删除标志（0代表未删除，1代表已删除） |

##### 表2: article_tag (文章标签关联表)

此表是一个关联表，用于建立文章与标签之间的多对多关系。通过此表，一篇文章可以关联多个标签，一个标签也可以应用于多篇文章。

| **字段名** | **数据类型** | **主键或外键** | **字段值约束**           | **说明** |
| ---------- | ------------ | -------------- | ------------------------ | -------- |
| article_id | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT | 文章id   |
| tag_id     | bigint       | PRIMARY KEY    | NOT NULL, DEFAULT 0      | 标签id   |

##### 表3: category (分类表)

此表用于定义和管理文章的分类。它支持层级结构（通过`pid`字段），可以创建父分类和子分类，方便对文章进行归类整理。

| **字段名**  | **数据类型** | **主键或外键** | **字段值约束**           | **说明**                             |
| ----------- | ------------ | -------------- | ------------------------ | ------------------------------------ |
| id          | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT |                                      |
| name        | varchar(128) |                | NULL DEFAULT NULL        | 分类名                               |
| pid         | bigint       |                | NULL DEFAULT -1          | 父分类id，如果没有父分类为-1         |
| description | varchar(512) |                | NULL DEFAULT NULL        | 描述                                 |
| status      | char(1)      |                | NULL DEFAULT '0'         | 状态0:正常,1禁用                     |
| create_by   | bigint       |                | NULL DEFAULT NULL        |                                      |
| create_time | datetime     |                | NULL DEFAULT NULL        |                                      |
| update_by   | bigint       |                | NULL DEFAULT NULL        |                                      |
| update_time | datetime     |                | NULL DEFAULT NULL        |                                      |
| del_flag    | int          |                | NULL DEFAULT 0           | 删除标志（0代表未删除，1代表已删除） |

##### 表4: comment (评论表)

此表用于存储用户对不同类型内容（如文章、友链）的评论。它支持评论的层级回复（通过`root_id`和`to_comment_id`），可以构建出完整的评论对话线程。

| **字段名**         | **数据类型** | **主键或外键** | **字段值约束**           | **说明**                                 |
| ------------------ | ------------ | -------------- | ------------------------ | ---------------------------------------- |
| id                 | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT |                                          |
| type               | char(1)      |                | NULL DEFAULT '0'         | 评论类型（0代表文章评论，1代表友链评论） |
| article_id         | bigint       |                | NULL DEFAULT NULL        | 文章id                                   |
| root_id            | bigint       |                | NULL DEFAULT -1          | 根评论id                                 |
| content            | varchar(512) |                | NULL DEFAULT NULL        | 评论内容                                 |
| to_comment_user_id | bigint       |                | NULL DEFAULT -1          | 所回复的目标评论的userid                 |
| to_comment_id      | bigint       |                | NULL DEFAULT -1          | 回复目标评论id                           |
| create_by          | bigint       |                | NULL DEFAULT NULL        |                                          |
| create_time        | datetime     |                | NULL DEFAULT NULL        |                                          |
| update_by          | bigint       |                | NULL DEFAULT NULL        |                                          |
| update_time        | datetime     |                | NULL DEFAULT NULL        |                                          |
| del_flag           | int          |                | NULL DEFAULT 0           | 删除标志（0代表未删除，1代表已删除）     |

##### 表5: link (友链表)

此表用于管理网站的友情链接。它记录了友情链接的名称、Logo、网址、描述以及审核状态，方便站长管理合作伙伴的链接。

| **字段名**  | **数据类型** | **主键或外键** | **字段值约束**           | **说明**                                               |
| ----------- | ------------ | -------------- | ------------------------ | ------------------------------------------------------ |
| id          | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT |                                                        |
| name        | varchar(256) |                | NULL DEFAULT NULL        |                                                        |
| logo        | varchar(256) |                | NULL DEFAULT NULL        |                                                        |
| description | varchar(512) |                | NULL DEFAULT NULL        |                                                        |
| address     | varchar(128) |                | NULL DEFAULT NULL        | 网站地址                                               |
| status      | char(1)      |                | NULL DEFAULT '2'         | 审核状态 (0代表审核通过，1代表审核未通过，2代表未审核) |
| create_by   | bigint       |                | NULL DEFAULT NULL        |                                                        |
| create_time | datetime     |                | NULL DEFAULT NULL        |                                                        |
| update_by   | bigint       |                | NULL DEFAULT NULL        |                                                        |
| update_time | datetime     |                | NULL DEFAULT NULL        |                                                        |
| del_flag    | int          |                | NULL DEFAULT 0           | 删除标志（0代表未删除，1代表已删除）                   |

##### 表6: sys_menu (菜单权限表)

此表是后台权限管理系统的核心，用于定义系统的功能菜单结构。它包括目录、菜单和按钮三个层级，并定义了每个菜单项的路由、组件路径、权限标识和可见状态。

| **字段名**  | **数据类型** | **主键或外键** | **字段值约束**           | **说明**                      |
| ----------- | ------------ | -------------- | ------------------------ | ----------------------------- |
| id          | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT | 菜单ID                        |
| menu_name   | varchar(50)  |                | NOT NULL                 | 菜单名称                      |
| parent_id   | bigint       |                | NULL DEFAULT 0           | 父菜单ID                      |
| order_num   | int          |                | NULL DEFAULT 0           | 显示顺序                      |
| path        | varchar(200) |                | NULL DEFAULT ''          | 路由地址                      |
| component   | varchar(255) |                | NULL DEFAULT NULL        | 组件路径                      |
| is_frame    | int          |                | NULL DEFAULT 1           | 是否为外链（0是 1否）         |
| menu_type   | char(1)      |                | NULL DEFAULT ''          | 菜单类型（M目录 C菜单 F按钮） |
| visible     | char(1)      |                | NULL DEFAULT '0'         | 菜单状态（0显示 1隐藏）       |
| status      | char(1)      |                | NULL DEFAULT '0'         | 菜单状态（0正常 1停用）       |
| perms       | varchar(100) |                | NULL DEFAULT NULL        | 权限标识                      |
| icon        | varchar(100) |                | NULL DEFAULT '#'         | 菜单图标                      |
| create_by   | bigint       |                | NULL DEFAULT NULL        | 创建者                        |
| create_time | datetime     |                | NULL DEFAULT NULL        | 创建时间                      |
| update_by   | bigint       |                | NULL DEFAULT NULL        | 更新者                        |
| update_time | datetime     |                | NULL DEFAULT NULL        | 更新时间                      |
| remark      | varchar(500) |                | NULL DEFAULT ''          | 备注                          |
| del_flag    | char(1)      |                | NULL DEFAULT '0'         |                               |

##### 表7: sys_role (角色信息表)

此表用于定义系统中的用户角色。每个角色都有一套独特的权限配置，通过角色可以将一组权限批量授予用户。

| **字段名**  | **数据类型** | **主键或外键** | **字段值约束**           | **说明**                        |
| ----------- | ------------ | -------------- | ------------------------ | ------------------------------- |
| id          | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT | 角色ID                          |
| role_name   | varchar(30)  |                | NOT NULL                 | 角色名称                        |
| role_key    | varchar(100) |                | NOT NULL                 | 角色权限字符串                  |
| role_sort   | int          |                | NOT NULL                 | 显示顺序                        |
| status      | char(1)      |                | NOT NULL                 | 角色状态（0正常 1停用）         |
| del_flag    | char(1)      |                | NULL DEFAULT '0'         | 删除标志（0代表存在 1代表删除） |
| create_by   | bigint       |                | NULL DEFAULT NULL        | 创建者                          |
| create_time | datetime     |                | NULL DEFAULT NULL        | 创建时间                        |
| update_by   | bigint       |                | NULL DEFAULT NULL        | 更新者                          |
| update_time | datetime     |                | NULL DEFAULT NULL        | 更新时间                        |
| remark      | varchar(500) |                | NULL DEFAULT NULL        | 备注                            |

##### 表8: sys_role_menu (角色和菜单关联表)

此表是角色与菜单权限之间的关联表，用于实现基于角色的访问控制（RBAC）。它明确了哪个角色拥有访问哪些菜单（包括页面和操作按钮）的权限。

| **字段名** | **数据类型** | **主键或外键** | **字段值约束** | **说明** |
| ---------- | ------------ | -------------- | -------------- | -------- |
| role_id    | bigint       | PRIMARY KEY    | NOT NULL       | 角色ID   |
| menu_id    | bigint       | PRIMARY KEY    | NOT NULL       | 菜单ID   |



##### 表9: sys_user_role (用户和角色关联表)

此表是用户与角色之间的关联表。通过此表，可以为每个用户分配一个或多个角色，从而让用户继承这些角色所拥有的全部权限。

| **字段名** | **数据类型** | **主键或外键** | **字段值约束** | **说明** |
| ---------- | ------------ | -------------- | -------------- | -------- |
| user_id    | bigint       | PRIMARY KEY    | NOT NULL       | 用户ID   |
| role_id    | bigint       | PRIMARY KEY    | NOT NULL       | 角色ID   |

##### 表10: tag (标签表)

此表用于存储所有可用的标签。这些标签可以被关联到文章上，方便用户通过标签快速查找相关内容。

| **字段名**  | **数据类型** | **主键或外键** | **字段值约束**           | **说明**                             |
| ----------- | ------------ | -------------- | ------------------------ | ------------------------------------ |
| id          | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT |                                      |
| name        | varchar(128) |                | NULL DEFAULT NULL        | 标签名                               |
| create_by   | bigint       |                | NULL DEFAULT NULL        |                                      |
| create_time | datetime     |                | NULL DEFAULT NULL        |                                      |
| update_by   | bigint       |                | NULL DEFAULT NULL        |                                      |
| update_time | datetime     |                | NULL DEFAULT NULL        |                                      |
| del_flag    | int          |                | NULL DEFAULT 0           | 删除标志（0代表未删除，1代表已删除） |
| remark      | varchar(500) |                | NULL DEFAULT NULL        | 备注                                 |

##### 表11: user (用户表)

此表是系统的核心用户表，用于存储用户的基本信息，包括登录账号（用户名）、密码、昵称、联系方式（邮箱、手机号）以及账户状态和类型（普通用户/管理员）。

| **字段名**  | **数据类型** | **主键或外键** | **字段值约束**           | **说明**                             |
| ----------- | ------------ | -------------- | ------------------------ | ------------------------------------ |
| id          | bigint       | PRIMARY KEY    | NOT NULL, AUTO_INCREMENT | 主键                                 |
| user_name   | varchar(64)  |                | NOT NULL, DEFAULT 'NULL' | 用户名                               |
| nick_name   | varchar(64)  |                | NOT NULL, DEFAULT 'NULL' | 昵称                                 |
| password    | varchar(64)  |                | NOT NULL, DEFAULT 'NULL' | 密码                                 |
| type        | char(1)      |                | NULL DEFAULT '0'         | 用户类型：0代表普通用户，1代表管理员 |
| status      | char(1)      |                | NULL DEFAULT '0'         | 账号状态（0正常 1停用）              |
| email       | varchar(64)  |                | NULL DEFAULT NULL        | 邮箱                                 |
| phonenumber | varchar(32)  |                | NULL DEFAULT NULL        | 手机号                               |
| sex         | char(1)      |                | NULL DEFAULT NULL        | 用户性别（0男，1女，2未知）          |
| avatar      | varchar(128) |                | NULL DEFAULT NULL        | 头像                                 |
| create_by   | bigint       |                | NULL DEFAULT NULL        | 创建人的用户id                       |
| create_time | datetime     |                | NULL DEFAULT NULL        | 创建时间                             |
| update_by   | bigint       |                | NULL DEFAULT NULL        | 更新人                               |
| update_time | datetime     |                | NULL DEFAULT NULL        | 更新时间                             |
| del_flag    | int          |                | NULL DEFAULT 0           | 删除标志（0代表未删除，1代表已删除） |

![image-20250619204306721](/F:/Typora_images/软件创新课程设计报告/image-20250619204306721.png)

## 3.2功能模块设计

根据需求分析将系统分为前台业务系统和后台管理系统。

前台业务系统，该模块面向的用户是患者，基础功能有九个模块，分别为实名认证、门诊充值、预约挂号、签到取号、就医评价、预约记录、平台公告、就诊人管理和报告单查看。特色功能有四个模块，分别为智能推荐、抢号提醒、附近药店、病案复印。

后台管理系统，该模块面向的用户是管理员，分为医院设置管理、数据管理、用户管理、订单管理、医院信息管理、抢号提醒管理、就医评价管理和公告管理，主要是对用户操作前台的数据进行管理，方便管理员对后台的数据进行统计与分析。

通过上面的描述可以得出预约挂号系统的功能模块脑图，如图7所示。

![](莆田学院课程考核评分表.assets/功能模块脑图.png)

<div align = "center">图7 功能模块脑图(StudInfor)</div>	

## 3.3项目整体设计

系统开发采用前后端分离的模式，前台业务系统的前端采用Vue.js技术对后端返回的JSON数据进行动态的交互，同时利用Element UI组件库对界面进行美化；后台管理系统的前端使用Nuxt框架进行二次开发，同时利用ECharts对统计模块的数据进行图表展示。系统后端采用Spring Boot和Spring Cloud搭建项目框架；使用Redis缓存验证码的有效时间和支付二维码的有效时间;使用Easy Excel操作Excel表格，实现数据的导入和导出；使用Mybatis Plus操作MySQL数据库，完成对数据的存储。

### 3.3.1项目架构设计

系统后端根据模块划分成不同的微服务模块,通过Spring Cloud Gateway网关作为项目的统一入口，所有的请求都需要经过网关进行转发。当用户登录成功后，系统会将用户信息转化为Jwt字符串，把该字符串用作用户的Token保存在Redis中，当用户下一次登录时，系统可以直接解析Token获取用户信息。使用Nacos进行服务的注册与发现，可以通过Nacos查看正常运行的微服务列表。使用Open Feign进行微服务中的通信，阿里OSS用作系统中图片的存储，采用阿里云短信服务发送短信。项目的架构图如图8所示。

![](莆田学院课程考核评分表.assets/项目架构图.png)

<div align = "center">图8 项目架构图</div>	

![](莆田学院课程考核评分表.assets/项目架构图2.png)

<div align = "center">图8 项目架构图</div>	

注意：这里需要画一个系统的架构图，风格不限，只要能呈现系统的架构即可，比如图8图9都可以，但是务必要根据实际情况替换，不要出现和实际项目架构不同的情况，建议自己重新画一个，避免出现大面积雷同，比如业务层中的【bean管理】可以去掉，还应该根据毕设实际使用的框架做适当补充。如果没有使用Redis，那么数据库中【Redis数据缓存】也要去掉。另外【嵌入已有app】应该根据毕设的实际情况修改，比如修改成浏览器或者微信小程序或者android app。本架构图基本适合大部分单体项目，如果是微服务的架构则需另外设计。

### 3.3.2项目模块设计

系统后端使用Spring Cloud技术进行开发，将项目的父目录命名为yygh，在该目录下分别划分为五个模块，其中server-gateway模块主要对网关的过滤器进行配置，common模块主要对项目的service模块中的公用工具类进行配置，model模块主要存放项目中的实体类，service模块主要对项目中的功能模块进行详细实现，service-client模块主要用于处理service模块中不同微服务之间的远程调用。

service模块根据项目的功能需求分别划分为9个子模块，service-msm模块主要用于处理阿里云短信服务接口的对接，service-oss模块主要用于处理阿里云的对象存储服务，用于项目中图片的存储，service-statistics模块主要用于处理后台管理的统计业务，service-task模块主要用于功能需求中的定时任务的处理，service-cmn模块主要处理后端的数据字典服务，service-hosp模块主要对医院相关的服务进行处理，service-order模块主要对医院预约挂号的订单进行处理，service-user主要对用户的相关服务进行处理，service-expressage模块主要对病案复印的物流查询模块进行处理，项目结构设计图如图9所示。

![](莆田学院课程考核评分表.assets/项目结构设计图.png)

<div align = "center">图9 项目结构设计图</div>	

## 3.4关键业务设计

本节可以是对系统比较关键且有一定难度的业务功能点（往往是一些需求痛点）进行解决方案说明。

### 3.4.1预约挂号

用户选择医院等级和医院地区，前端将用户选择的结果发送给后端，后端返回符合用户选择的医院列表，用户选择医院进入医院详情界面，点击该医院导航栏的“预约挂号”按钮，进入预约挂号页面，后端查询医院的科室树形列表返回给前端。用户选择科室，后端查询该科室的医生排班列表返回给前端，用户选择医院和医生，进入确认挂号信息页面，选择就诊人和预约时间段，点击“确认挂号”按钮，后端将用户的预约挂号信息存入订单表，前端跳转到挂号详情页面，展示预约挂号的订单信息，预约挂号流程图如图11所示。

![](莆田学院课程考核评分表.assets/预约挂号流程.png)

<div align = "center">图11 预约挂号流程图</div>	

### 3.4.2智能推荐

用户进入智能推荐功能，前端界面展示与机器人客服聊天的对话框，当用户输入的关键词与前端机器人客服设定的问题模糊配置时，机器人自动回复该问题的答案，机器人客服预设三个可以智能推荐的问题，如果用户输入的问题不符合预设的问题，返回预设问题的列表。当用户提问关于找医生的问题，机器人客服回复请求用户输入医生姓名，后端根据医生名字查找该医生的排班日期；当用户提问关于找科室的问题时，机器人客服回复请求用户输入科室名称，后端根据科室名称查找包含该科室的医院；当用户提问关于找医院的问题时，机器人客服回复请求用户输入医院名称，后端根据医院名称返回该医院的详情信息，智能推荐流程如图12所示。

![](莆田学院课程考核评分表.assets/智能推荐流程图.png)

<div align = "center">图12 智能推荐流程图</div>	

# 4关键技术实现



要求说明：实现你系统的一些关键技术或者是比较特殊的技术，如系统用到了OSS和地图API的技术、云开发，云函数和相关API等。

### 4.1图片的存储

本系统中用户所上传的图片资源都是上传到阿里云的OSS对象存储中的。阿里云对象存储（Object Storage Service，简称OSS）是阿里云提供的一种海量、安全、低成本、高可靠的云存储服务。OSS是一种面向互联网的分布式存储服务，可以用于存储和访问各种类型的数据，包括文本、图片、音频、视频等。图片存储的工作过程如图9所示，关键代码实现如图10所示，先生成一个UUID来充当图片的文件名防止命名重复，然后将图片上传到OOS中，OSS会返回图片的url，将图片的url存到数据库后，以后就可以直接通过url访问图片了。

![](莆田学院课程考核评分表.assets/上传图片流程图.png)

<div align = "center">图9  上传图片流程图</div>	

### 4.2 高德地图API

本系统使用高德地图API的地方主要有：在发起拼车时完成选址以及查看拼车详情时的路线规划。这里主要介绍一下选址的实现，在前端工程中，使用Vue框架定义一个Vue选址组件：CustomChooseAddress，关键代码如图12所示。

（1）在template标签中定义了一个名为map-address的div容器，该容器内部包含一个地图的显示框、一个搜索框和一个地址列表。这些元素可以通过CSS样式进行美化。

（2）在script标签中，定义了一个名为CustomChooseAddress的Vue组件，其中通过data选项定义了该组件的初始数据，包括地图中心点坐标、地图标记、搜索框中的关键词、地图实例和搜索结果等。methods选项定义了该组件的方法，包括关键词搜索、异步地图自动完成和地址选择等。

（3）在keySearch方法中，如果搜索框中没有输入内容，则会提示用户输入地址。接着，通过AMap.plugin方法引入了一个名为AMap.PlaceSearch的插件，并在回调函数中执行地点搜索。如果搜索成功，则将搜索结果保存在searchResult中，并在地图上添加一个标记，同时将地图中心点定位到搜索结果的第一个地址。如果搜索失败，则会提示用户搜索失败。

（4）在querySearchAsync方法中，创建了一个AMap.Autocomplete对象，并在回调函数中执行地点自动完成。如果自动完成成功，则将结果保存在searchResult中，并在回调函数中执行cb(tips)，将结果传递给回调函数。如果自动完成失败，则不进行任何操作。

（5）在selectResult方法中，通过$createDialog方法创建了一个对话框，用于显示地址的详细信息，并提供了一个确认按钮和一个取消按钮。如果用户点击确认按钮，则将选择的地址信息保存在本地存储中，并在地图上添加一个标记，并将地图中心点定位到选择的地址。最后，返回上一页。如果用户点击取消按钮，则不进行任何操作。

（6）在mounted钩子函数中，初始化地图并设置中心点。如果初始化成功，则获取当前地图中心点，并添加一个标记。

<div align = "center">选址组件template代码实现</div>	

```html
<template>
<div class="map-address"
<div id="map" style="height: 390px"></div><div class="search">
<input
class="search-inputtype="text"
id="pickerInput"placehalder="请输入关键词@keyup.enter="keysearch"v-model="address
</div>
<div class="address-list">
<ul>
<li
v-for="item in searchResult":key="item.id"@click="selectResult(item)
<p class="name">{{ item.name }}</p><p class="detail">{{ item.address }}/p></li>
</ul>
</div>
</div>
</template>
```



# 5 业务模块实现

本节主要撰写各个功能模块的实现，根据系统不同，可以在细分为前台系统和后台系统的各个功能模块来详细说明。需要结合业务详细说明每个模块的功能是什么样的，业务逻辑是什么，有什么管控点，如果有一些比较特殊的点，可以适当截图代码，说明怎么实现的。如果业务有一定的复杂度，还可以补充一些业务**流程图或者时序图**，一方面便于读者理解，另一方面也增加篇幅提现工作量。

功能模块在介绍系统的核心模块时，需要完整介绍业务功能和代码实现，从前端js管控，ajax请求，到后端control层请求参数接收，到service层业务逻辑实现，最后到DAO层数据库存储，需要代码截图并解释代码含义，让读者能看懂。

另外，如果使用后端管理系统框架，如若依等，在一些通用模块上简单介绍即可，就不要大量代码说明和篇幅介绍，比如登录模块，菜单管理等，你们业务相关的的篇幅必须满足工作量要求。



<div align = "center" style= "color:red;font-size:40px">按照课程要求，需要优先填写新增模块</div>	

## 5.1用户管理(超级管理员)

## 5.2 角色管理(超级管理员)

## 5.3 菜单管理(超级管理员)

## 5.4文章管理(普通管理员)

## 5.5 分类管理(普通管理员)

## 5.6友链管理(友链管理员)





### 5.1用户管理（某系统普通模块实现）

使用了 ElementUI 组件库来实现表单、表格和按钮等基本组件。通过v`-`model指令和@click事件绑定来实现双向数据绑定和按钮点击事件的处理。其中的v`-`hasPermi是自定义指令，用于控制权限，判断用户是否有权限进行操作。另外还使用了自定义的right`-`toolbar组件，来实现表格上方的搜索和操作按钮的布局和交互逻辑。整体实现的功能是一个用户管理的页面，可以对用户进行查询、新增、修改、删除和导出等操作。用户管理如图49所示。

![](莆田学院课程考核评分表.assets/用户管理图.png)

<div align = "center">图49  用户管理图</div>	

### 5.2 购物车模块的实现（某系统普通功能模块实现）

先介绍功能和功能实现，再进行代码描述

购物车模块由2部分组成，由上而下依次是：购物车清单、常购清单。游客和用户将待选购商品加入购物车后即可显示在购物车中，加入购物车后各项商品前的选择框默认为选中状态、商品数量默认为1，游客和用户可点击计数器增减商品数量。若当商品数量减至0时，可将本商品删除；若点击右上方清空购物车，可将购物车中商品全部清除；若当商品数量增加超过商品最大库存量，则计数器“＋”禁用。只有当游客授权登录成为普通用户后，购物车中常购清单才可显示所有常购商品。用户可在购物车常购清单下，直接点击加入购物车进行购买操作，若加购的常购商品已存在于购物车中，则会弹出提示语句提示用户可直接在购物车中修改数量或直接购买。

购物车页面下方工具栏部分会显示是否全选、合计总价以及结算按钮（包括展示已选购的商品总数）。游客或用户修改商品选择框选择状态时，工具栏中的全选按钮状态、合计价格数值和已选购商品总数也会随之发生变化。当购物车中所有待选购商品都被选中时，全选按钮自动变为选中状态，其他情况默认为未选中状态；当游客或用户点击取消全选或修改购物车中某一待选购商品的状态为未选中时，全选按钮自动变为未选中状态。 

![](莆田学院课程考核评分表.assets/购物车界面.png)

<div align = "center">图18  购物车模块界面</div>	

具体实现上，用户点击某个商品加入购物车按钮时，通过wx.getStorageSync获取本地存储中的cart下所有购物车商品信息，利用findIndex查询本地存储cart即购物车中是否已添加过当前商品，findIndex会返回查询结果的索引值index（不存在时索引值index为-1），如图19所示。若购物车中已存在当前商品，index为当前商品在本地存储cart中的索引下标，点击加购时直接将购物车中该商品的count加1即可；若购物车中不存在当前商品，index为默认值-1，此时将商品加入购物车中并设置商品状态为选中状态（checked为true）且该商品的数量count默认为1。最后，使用wx.setStorageSync更新本地存储cart购物车的数据。

游客/用户在购物车中修改各项商品的选中状态时，都会触发setCart方法重新更新购物车底部工具栏中全选的状态并计算总数量、总价等数据，如图20所示。初始时设置全选框状态checkedAll为false，合计总价priceTotal为0，已选择商品总数量totalNumber为0。根据forEach遍历setCart方法中传入的最新cart购物车商品信息，当所有商品中存在某一项商品的checked状态为false时，全选状态checkedAll即修改为false，同时累加已选中商品的count及单项商品总价price*count得出已选择商品总数量totalNumber及合计总价priceTotal。最后，利用split根据"."将总价priceTotal分割为整数和小数部分，判断小数部分的长度length是否超过2位，若超出则使用toFixed()方法将字符串priceTotal四舍五入为指定的小数位数2。

<div align = "center">加入购物车主要代码</div

```javascript
// 加入购物车
handleProductAdd()f
this.setcartstorageAdd();
wx.showToast(f
title:'添加成功'
icon:'success'
mask:true
setCartstorageAdd()f
let cart = wx.getstoragesync('cart')[];let index = cart.findIndex(v =>v.id === this.productInfo.id);if(index === -1){
this.productInfo.count = 1;
this.productInfo.checked = true;cart.push(this.productInfo);lelse f
cart[indexl.count++;
wx.setStorageSync('cart',cart);
},
```

<div align = "center">购物车总价及数量统计主要代码</div

```javascript
setCart(cart)
let checkedAll=true;
let priceTotal=0;
let totalNumber =0;
cart.forEach(element =>
if (!element.checked)checkedAll =false;
else f
totalNumber += element.count;
priceTotal +=element.price *element.count;
});
checkedAll=cart.length !=0 ?checkedAll :false;priceTotal = priceTotal,tostring().split(".")[1].length >2 ? priceTotal.tofixed(2): pricelotal;this.setData(f
cart,
checkedAll
priceTotal,
totalNumber
wx.setStoragesync('cart',cart);
},
```

# 6代码附录

## 6.1xxx功能

### 6.1.1xxx功能实现方法

在实现登录的功能中主要采用了jwt的工具类作为服务器端的无状态验证方式，方便了系统的实现， 主要是做到了利用用户第一次登录时的用户名和密码通过参数的行式传给了后端，后端利用了jwt工具将 这些信息打包封装成token信息，然后又将这些信息传给了前端，前端又通过axios跨域请求了后端的数 据，获得数据之后将其存入了前端中，进行第二次登录的时候就可以直接带着存在前端的token信息通 过axios跨域请求去访问后端的controller控制层对应的方法然后去校验token信息的内容，如果校验成功 就会直接通过登录，将登录成功的code数值传给前端，前端得到信息后会做出跳转页面的响应。

### 6.1.2xxx接口设计

| 请求方式 | 请求路径 |
| -------- | -------- |
| POST     | /login   |

### 6.1.3请求体

```javascript
{
 "userName":"test",
 "password":"1234"
 }
```

### 6.1.4响应格式

```javascript
{
 "code": 200,
 "data": {
 "token":
 "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI0ODBmOThmYmJkNmI0NjM0OWUyZjY2NTM0NGNjZWY2NSIsI
 nN1YiI6IjEiLCJpc3MiOiJzZyIsImlhdCI6MTY0Mzg3NDMxNiwiZXhwIjoxNjQzOTYwNzE2fQ.ldLBUv
 NIxQCGemkCoMgT_0YsjsWndTg5tqfJb77pabk",
 "userInfo": {
 "avatar":
 }
 "http://i0.hdslb.com/bfs/article/3bf9c263bc0f2ac5c3a7feb9e218d07475573ec8.gif",
 "email": "<EMAIL>",
 "id": 1,
 "nickName": "test",
 "sex": "1"
 }
  "msg": "操作成功"
 },

```

### 5.1.5代码实现

思路分析 （1）自定义登录接口 调用ProviderManager的方法进行认证 如果认证通过生成jwt  把用户信息存入redis中 （2）自定义UserDetailsService 在这个实现类中去查询数据库 注意配置passwordEncoder为BCryptPasswordEncode



 BlogLoginController:blog模块

```java
@RestController
 public class BlogLoginController {
 @Autowired
 private IBlogLoginService blogLoginService;
 @PostMapping("/login")
 public ResponseResult login(@RequestBody User user){
 return blogLoginService.login(user);
 }
 }
```

 IBlogLoginService：framework模块

```java
public interface IBlogLoginService {
 ResponseResult login(User user);
 }
```

SecurityConfig:blog模块

```java
@Configuration
 public class SecurityConfig {
 @Bean
 public PasswordEncoder passwordEncoder(){
 return new BCryptPasswordEncoder();
 }
 @Bean
 public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
 http
 //关闭csrf
 .csrf().disable()
 //不通过Session获取SecurityContext
 .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
 .and()
 .authorizeRequests()
 // 对于登录接口 允许匿名访问
.antMatchers("/login").anonymous()
 // 除上面外的所有请求全部不需要认证即可访问
.anyRequest().permitAll();
 http.logout().disable();
 //允许跨域
http.cors();
 return http.build();
 }
 @Bean
public AuthenticationManager authenticationManager(AuthenticationConfiguration
 authenticationConfiguration) throws Exception {
 return authenticationConfiguration.getAuthenticationManager();
 }
 }
```

BlogLoginServiceImpl:framework模块

```java
@Service
 public class BlogLoginServiceImpl implements IBlogLoginService {
 @Autowired
 private AuthenticationManager authenticationManager;
 @Autowired
 private RedisCache redisCache;
 @Override
 public ResponseResult login(User user) {
 UsernamePasswordAuthenticationToken authenticationToken = new
 UsernamePasswordAuthenticationToken(user.getUserName(),user.getPassword());
 Authentication authenticate =
 authenticationManager.authenticate(authenticationToken);
 //判断是否认证通过
if(Objects.isNull(authenticate)){
 throw new RuntimeException("用户名或密码错误");
 }
 //获取userid 生成token
 LoginUser loginUser = (LoginUser) authenticate.getPrincipal();
 String userId = loginUser.getUser().getId().toString();
 String jwt = JwtUtil.createJWT(userId);
 //把用户信息存入redis
 redisCache.setCacheObject("bloglogin:"+userId,loginUser);
 //把token和userinfo封装 返回
//把User转换成UserInfoVo
 UserInfoVo userInfoVo = new UserInfoVo();
 BeanUtils.copyProperties(loginUser.getUser(), userInfoVo);
 BlogUserLoginVo vo = new BlogUserLoginVo(jwt,userInfoVo);
 return ResponseResult.okResult(vo);
 }
 }
```

 UserDetailsServiceImpl:framework模块

```java
@Service
 public class UserDetailsServiceImpl implements UserDetailsService {
 @Autowired
 private UserMapper userMapper;
 @Override
 public UserDetails loadUserByUsername(String username) throws
 UsernameNotFoundException {
 //根据用户名查询用户信息
LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
 queryWrapper.eq(User::getUserName,username);
 User user = userMapper.selectOne(queryWrapper);
 //判断是否查到用户 如果没查到抛出异常
if(Objects.isNull(user)){
}
```

LoginUser:framework模块

```java
@Data
 @AllArgsConstructor
 @NoArgsConstructor
 public class LoginUser implements UserDetails {
 private User user;
 @Override
 public Collection<? extends GrantedAuthority> getAuthorities() {
 return null;
 }
 @Override
 public String getPassword() {
 return user.getPassword();
 }
 @Override
 public String getUsername() {
 return user.getUserName();
 }
 @Override
 public boolean isAccountNonExpired() {
 return true;
 }
 @Override
 public boolean isAccountNonLocked() {
 return true;
 }
 @Override
 public boolean isCredentialsNonExpired() {
 return true;
 }
 @Override
 public boolean isEnabled() {
 return true;
 }
 }
```

 BlogUserLoginVo

```java
 @Data
 @NoArgsConstructor
 @AllArgsConstructor
 public class BlogUserLoginVo {
 private String token;
 private UserInfoVo userInfo;
 }
```

 UserInfoVo

```java
@Data
 @Accessors(chain = true)
 public class UserInfoVo {
 /**
 * 主键
*/
 private Long id;
 /**
 * 昵称
*/
 private String nickName;
 /**
 * 头像
*/
 private String avatar;
 private String sex;
 private String email;
 }
```

