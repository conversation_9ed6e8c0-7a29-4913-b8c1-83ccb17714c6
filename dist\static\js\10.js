webpackJsonp([10],{fXMu:function(s,e){},psK5:function(s,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var t=a("Cz8s"),r=a("mzkE"),i=a("1pQF"),o={name:"UserInfo",data:function(){return{isEdit:!1,userInfo:"",userInfoObj:"",state:!0,usertabChosed:"天然呆",usertab:["天然呆","小萌新","学霸","萌萌哒","技术宅","忠实粉"],wwwHost:"http://"+window.location.host}},methods:{handleAvatarSuccess:function(s,e){1001==s.code?(this.userInfoObj.avatar=s.image_name,this.userInfoObj.head_start=1):this.$message.error("上传图片失败")},beforeAvatarUpload:function(s){var e="image/png"==s.type||"image/jpg"==s.type||"image/jpeg"==s.type,a=s.size/1024/1024<1;return e||this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!"),a||this.$message.error("上传头像图片大小不能超过 1MB!"),e&&a},handleLogoSuccess:function(s,e){1001==s.code?(this.userInfoObj.image=s.image_name,this.userInfoObj.logo_start=1):this.$message.error("上传图片失败")},beforeLogoUpload:function(s){var e="image/png"==s.type||"image/jpg"==s.type||"image/jpeg"==s.type,a=s.size/1024/1024<1;return e||this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!"),a||this.$message.error("上传头像图片大小不能超过 1MB!"),e&&a},saveInfoFun:function(){var s=this;if(s.userInfoObj.username){if(s.state){if(!s.userInfoObj.url||!/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(s.userInfoObj.url))return void s.$message.error("请正确填写网址，如http://www.xxx.com");if(!s.userInfoObj.name)return void s.$message.error("请填写网站名称");if(!s.userInfoObj.description)return void s.$message.error("请填写网站简介")}s.userInfoObj.state=Number(s.state),Object(i.l)(s.userInfoObj,function(e){s.$message.success("保存成功！"),s.isEdit=!1,s.routeChange()})}else s.$message.error("昵称为必填项，请填写昵称")},routeChange:function(){var s=this;localStorage.getItem("userInfo")?(s.haslogin=!0,s.userInfo=JSON.parse(localStorage.getItem("userInfo")),s.userId=s.userInfo.userId,Object(i.s)(s.userId,function(e){s.userInfoObj=e.data,s.userInfoObj.head_start=0,s.userInfoObj.logo_start=0,s.state=1==e.data.state})):s.haslogin=!1}},components:{"wbc-nav":t.a,"wbc-footer":r.a},watch:{$route:"routeChange"},created:function(){this.routeChange()}},l={render:function(){var s=this,e=s.$createElement,a=s._self._c||e;return a("div",[a("wbc-nav"),s._v(" "),a("div",{staticClass:"container"},[a("div",{directives:[{name:"show",rawName:"v-show",value:s.isEdit,expression:"isEdit"}],staticClass:"tcommonBox"},[s._m(0),s._v(" "),a("section",[a("ul",{staticClass:"userInfoBox"},[a("li",{staticClass:"avatarlist"},[a("span",{staticClass:"leftTitle"},[s._v("头像")]),s._v(" "),a("el-upload",{staticClass:"avatar-uploader",attrs:{action:this.$store.state.host+"Userinfo/UploadImg","show-file-list":!1,"on-success":s.handleAvatarSuccess,"before-upload":s.beforeAvatarUpload}},[s.userInfoObj.avatar?a("img",{staticClass:"avatar",attrs:{src:s.userInfoObj.avatar?s.wwwHost+s.userInfoObj.avatar:"static/img/tou.jpg",onerror:s.$store.state.errorImg}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),s._v(" "),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[s._v("点击上传头像，只能上传jpg/png文件，且不超过1mb")])])],1),s._v(" "),a("li",{staticClass:"username"},[a("span",{staticClass:"leftTitle"},[s._v("昵称")]),s._v(" "),a("el-input",{attrs:{placeholder:"昵称"},model:{value:s.userInfoObj.username,callback:function(e){s.$set(s.userInfoObj,"username",e)},expression:"userInfoObj.username"}}),s._v(" "),a("i",{staticClass:"fa fa-wa fa-asterisk"})],1),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("电子邮件")]),s._v(" "),a("span",[s._v(s._s(s.userInfoObj.email))])]),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("性别")]),s._v(" "),[a("el-radio",{staticClass:"radio",attrs:{label:"0"},model:{value:s.userInfoObj.sex,callback:function(e){s.$set(s.userInfoObj,"sex",e)},expression:"userInfoObj.sex"}},[s._v("男")]),s._v(" "),a("el-radio",{staticClass:"radio",attrs:{label:"1"},model:{value:s.userInfoObj.sex,callback:function(e){s.$set(s.userInfoObj,"sex",e)},expression:"userInfoObj.sex"}},[s._v("女")])]],2),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("个性标签")]),s._v(" "),[a("el-radio-group",{model:{value:s.userInfoObj.label,callback:function(e){s.$set(s.userInfoObj,"label",e)},expression:"userInfoObj.label"}},s._l(s.usertab,function(e,t){return a("el-radio",{key:"usertab"+t,attrs:{label:e}},[s._v(s._s(e))])}))]],2),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("是否展示友链")]),s._v(" "),a("el-switch",{attrs:{"on-color":"#13ce66","off-color":"#aaa"},model:{value:s.state,callback:function(e){s.state=e},expression:"state"}})],1),s._v(" "),a("li",{directives:[{name:"show",rawName:"v-show",value:s.state,expression:"state"}]},[a("span",{staticClass:"leftTitle"},[s._v("网站名称")]),s._v(" "),a("el-input",{attrs:{placeholder:"网站名称"},model:{value:s.userInfoObj.name,callback:function(e){s.$set(s.userInfoObj,"name",e)},expression:"userInfoObj.name"}}),a("i",{directives:[{name:"show",rawName:"v-show",value:s.state,expression:"state"}],staticClass:"fa fa-wa fa-asterisk"})],1),s._v(" "),a("li",{directives:[{name:"show",rawName:"v-show",value:s.state,expression:"state"}]},[a("span",{staticClass:"leftTitle"},[s._v("网站地址")]),s._v(" "),a("el-input",{attrs:{placeholder:"网站",value:"userWeb"},model:{value:s.userInfoObj.url,callback:function(e){s.$set(s.userInfoObj,"url",e)},expression:"userInfoObj.url"}}),s._v(" "),a("i",{directives:[{name:"show",rawName:"v-show",value:s.state,expression:"state"}],staticClass:"fa fa-wa fa-asterisk"})],1),s._v(" "),a("li",{directives:[{name:"show",rawName:"v-show",value:s.state,expression:"state"}]},[a("span",{staticClass:"leftTitle"},[s._v("网站简介")]),s._v(" "),a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入内容"},model:{value:s.userInfoObj.description,callback:function(e){s.$set(s.userInfoObj,"description",e)},expression:"userInfoObj.description"}}),a("i",{directives:[{name:"show",rawName:"v-show",value:s.state,expression:"state"}],staticClass:"fa fa-wa fa-asterisk"})],1),s._v(" "),a("li",{directives:[{name:"show",rawName:"v-show",value:s.state,expression:"state"}],staticClass:"avatarlist"},[a("span",{staticClass:"leftTitle"},[s._v("网站logo")]),s._v(" "),a("el-upload",{staticClass:"avatar-uploader",attrs:{action:this.$store.state.host+"Userinfo/UploadImg","show-file-list":!1,"on-success":s.handleLogoSuccess,"before-upload":s.beforeLogoUpload}},[s.userInfoObj.image?a("img",{staticClass:"avatar",attrs:{src:s.userInfoObj.image?s.wwwHost+s.userInfoObj.image:"static/img/tou.jpg",onerror:s.$store.state.errorImg}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),s._v(" "),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[s._v("点击上传头像，只能上传jpg/png文件，且不超过1mb")])])],1)]),s._v(" "),a("div",{staticClass:" saveInfobtn"},[a("a",{staticClass:"tcolors-bg",attrs:{href:"javascript:void(0);"},on:{click:function(e){s.isEdit=!s.isEdit}}},[s._v("返 回")]),s._v(" "),a("a",{staticClass:"tcolors-bg",attrs:{href:"javascript:void(0);"},on:{click:s.saveInfoFun}},[s._v("保 存")])])])]),s._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!s.isEdit,expression:"!isEdit"}],staticClass:"tcommonBox"},[a("header",[a("h1",[s._v("\n                        个人中心\n                    "),a("span",{staticClass:"gotoEdit",on:{click:function(e){s.isEdit=!s.isEdit}}},[a("i",{staticClass:"fa fa-wa fa-edit"}),s._v("编辑")])])]),s._v(" "),a("section",[a("ul",{staticClass:"userInfoBox"},[a("li",{staticClass:"avatarlist"},[a("span",{staticClass:"leftTitle"},[s._v("头像")]),s._v(" "),a("div",{staticClass:"avatar-uploader"},[a("img",{staticClass:"avatar",attrs:{src:s.userInfoObj.avatar?s.wwwHost+s.userInfoObj.avatar:"static/img/tou.jpg",onerror:s.$store.state.errorImg}})])]),s._v(" "),a("li",{staticClass:"username"},[a("span",{staticClass:"leftTitle"},[s._v("昵称")]),s._v(" "),a("span",[s._v(s._s(s.userInfoObj.username?s.userInfoObj.username:"无"))])]),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("电子邮件")]),s._v(" "),a("span",[s._v(s._s(s.userInfoObj.email?s.userInfoObj.email:"无"))])]),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("性别")]),s._v(" "),a("span",[s._v(s._s(0==s.userInfoObj.sex?"男":"女"))])]),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("个性标签")]),s._v(" "),a("span",[s._v(s._s(s.userInfoObj.label?s.userInfoObj.label:"未设置"))])]),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("是否展示友链")]),s._v(" "),a("el-switch",{attrs:{disabled:""},model:{value:s.state,callback:function(e){s.state=e},expression:"state"}})],1),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("网站名称")]),s._v(" "),a("span",[s._v(s._s(s.userInfoObj.name?s.userInfoObj.name:"无"))])]),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("网站地址")]),s._v(" "),a("p",{staticClass:"rightInner"},[s._v(s._s(s.userInfoObj.url?s.userInfoObj.url:"无"))])]),s._v(" "),a("li",[a("span",{staticClass:"leftTitle"},[s._v("网站简介")]),s._v(" "),a("p",{staticClass:"rightInner"},[s._v(s._s(s.userInfoObj.description?s.userInfoObj.description:"无"))])]),s._v(" "),a("li",{staticClass:"avatarlist"},[a("span",{staticClass:"leftTitle"},[s._v("网站logo")]),s._v(" "),a("div",{staticClass:"avatar-uploader"},[a("img",{staticClass:"avatar",attrs:{src:s.userInfoObj.image?s.wwwHost+s.userInfoObj.image:"static/img/tou.jpg",onerror:s.$store.state.errorImg}})])])])])])]),s._v(" "),a("wbc-footer")],1)},staticRenderFns:[function(){var s=this.$createElement,e=this._self._c||s;return e("header",[e("h1",[this._v("\n                        编辑个人资料\n                ")])])}]};var n=a("VU/8")(o,l,!1,function(s){a("fXMu")},null,null);e.default=n.exports}});