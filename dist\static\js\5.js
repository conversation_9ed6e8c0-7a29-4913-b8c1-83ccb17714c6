webpackJsonp([5],{Lfk8:function(t,e){},caxs:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=s("Cz8s"),n=s("mzkE"),i=s("ZsVV"),r={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tcommonBox"},[this._m(0),this._v(" "),e("section",[e("p",{},[e("img",{staticClass:"aboutmeImg",attrs:{src:this.$store.state.aboutmeObj.image?this.$store.state.aboutmeObj.image:"static/img/maoto.png",alt:"",onerror:"this.onerror=null;this.src= 'static/img/maoto.png'"}})]),this._v(" "),e("p",{domProps:{innerHTML:this._s(this.$store.state.aboutmeObj.brief)}},[this._v(this._s(this.$store.state.aboutmeObj.brief))])])])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("header",[e("h1",[e("a",{attrs:{href:"#/DetailShare",target:"_blank"}},[this._v("\n                关于我\n            ")])])])}]};var o=s("VU/8")({data:function(){return{}},methods:{},components:{},created:function(){}},r,!1,function(t){s("Lfk8")},null,null).exports,c=s("9jkD"),u={name:"Aboutme",data:function(){return{}},methods:{},components:{"wbc-nav":a.a,"wbc-message":c.a,"wbc-about":o,"wbc-rightlist":i.a,"wbc-footer":n.a},created:function(){}},l={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("wbc-nav"),this._v(" "),e("div",{staticClass:"container"},[e("el-row",{attrs:{gutter:30}},[e("el-col",{staticStyle:{transition:"all .5s ease-out","margin-bottom":"30px"},attrs:{sm:24,md:16}},[e("wbc-about"),this._v(" "),e("wbc-message")],1),this._v(" "),e("el-col",{attrs:{sm:24,md:8}},[e("wbc-rightlist")],1)],1)],1),this._v(" "),e("wbc-footer")],1)},staticRenderFns:[]};var m=s("VU/8")(u,l,!1,function(t){s("pUY9")},null,null);e.default=m.exports},pUY9:function(t,e){}});